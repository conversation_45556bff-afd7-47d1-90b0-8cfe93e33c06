import { useState, useEffect } from "react";
import { Plus, X, Check, Edit, Trash2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toast } from "@/components/ui/use-toast";
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext";
import { useSupabaseSubjectStore } from "@/stores/supabaseSubjectStore";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { HexColorPicker } from "react-colorful";

export interface Subject {
  id: string;
  name: string;
  color?: string;
}

interface SubjectManagerProps {
  selectedSubject: Subject | null;
  onSubjectChange: (subject: Subject | null) => void;
}

// Predefined color palette for subjects
const PREDEFINED_COLORS = [
  "#4f46e5", // Indigo
  "#ec4899", // Pink
  "#06b6d4", // Cyan
  "#f97316", // Orange
  "#10b981", // Emerald
  "#8b5cf6", // Violet
  "#f43f5e", // Rose
  "#0ea5e9", // Sky
  "#84cc16", // Lime
  "#14b8a6", // Teal
  "#d946ef", // Fuchsia
  "#f59e0b"  // Amber
];

export function SubjectManager({ selectedSubject, onSubjectChange }: SubjectManagerProps) {
  const { user } = useSupabaseAuth();
  const { subjects, isLoading, fetchSubjects, addSubject, updateSubject, deleteSubject } = useSupabaseSubjectStore();
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [newSubjectName, setNewSubjectName] = useState("");
  const [newSubjectColor, setNewSubjectColor] = useState(PREDEFINED_COLORS[Math.floor(Math.random() * PREDEFINED_COLORS.length)]);
  const [editingSubject, setEditingSubject] = useState<Subject | null>(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // Load subjects using the Supabase store
  useEffect(() => {
    if (user) {
      fetchSubjects(user.id);
    }
  }, [user, fetchSubjects]);

  // Add a new subject
  const handleAddSubject = async () => {
    if (!user || !newSubjectName.trim()) return;

    try {
      const newSubjectData = await addSubject(user.id, newSubjectName.trim(), newSubjectColor);
      onSubjectChange(newSubjectData); // Select the new subject
      setNewSubjectName("");
      setNewSubjectColor(PREDEFINED_COLORS[Math.floor(Math.random() * PREDEFINED_COLORS.length)]);
      setIsAddDialogOpen(false);
      toast({
        title: "Subject added",
        description: `"${newSubjectData.name}" has been added successfully.`,
      });
    } catch (error) {
      console.error("Error in handleAddSubject:", error);
      toast({
        title: "Error",
        description: "Failed to add subject. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Edit an existing subject
  const handleEditSubject = async () => {
    if (!user || !editingSubject) return;
    try {
      const updatedSubject = await updateSubject(editingSubject.id, {
        name: editingSubject.name,
        color: editingSubject.color,
      });
      if (selectedSubject && selectedSubject.id === editingSubject.id) {
        onSubjectChange(updatedSubject);
      }
      setEditingSubject(null);
      setIsEditDialogOpen(false);
      toast({
        title: "Subject updated",
        description: `"${updatedSubject.name}" has been updated successfully.`,
      });
    } catch (error) {
      console.error("Error in handleEditSubject:", error);
      toast({
        title: "Error",
        description: "Failed to update subject. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Delete a subject
  const handleDeleteSubject = async (subjectToDelete: Subject) => {
    if (!user) return;

    if (!window.confirm(`Are you sure you want to delete "${subjectToDelete.name}"? This action cannot be undone.`)) {
      return;
    }

    try {
      await deleteSubject(subjectToDelete.id);
      if (selectedSubject && selectedSubject.id === subjectToDelete.id) {
        onSubjectChange(null); // Deselect if deleted
      }
      toast({
        title: "Subject deleted",
        description: `"${subjectToDelete.name}" has been deleted successfully.`,
      });
    } catch (error) {
      console.error("Error in handleDeleteSubject:", error);
      toast({
        title: "Error",
        description: "Failed to delete subject. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="w-fit">
      {/* Subject Selector */}
      <Popover open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={isDropdownOpen}
            className="w-fit min-w-[15rem] justify-center relative group transition-all duration-300 bg-background/50 dark:bg-transparent border border-border dark:border-white/10 text-foreground dark:text-white hover:bg-accent dark:hover:bg-white/5"
          >
            {selectedSubject ? (
              <div className="flex items-center justify-center gap-2 w-fit">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: selectedSubject.color }}
                />
                <span className="text-center">{selectedSubject.name}</span>
              </div>
            ) : (
              <span className="text-center text-muted-foreground">Select subject...</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0 bg-background/90 dark:bg-[#1a1f3c]/90 backdrop-blur-md border border-border dark:border-white/10" align="start">
          <div className="max-h-[300px] overflow-y-auto">
            {subjects.length > 0 ? (
              <div className="grid grid-cols-1 gap-1 p-1">
                {subjects.map((subject) => (
                  <div
                    key={subject.id}
                    className={cn(
                      "group flex items-center justify-between px-2 py-1.5 rounded-md cursor-pointer transition-colors",
                      selectedSubject?.id === subject.id
                        ? "bg-primary/10 text-primary"
                        : "hover:bg-muted"
                    )}
                    onClick={() => {
                      onSubjectChange(subject);
                      setIsDropdownOpen(false);
                    }}
                  >
                    <div className="flex items-center gap-2">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: subject.color }}
                      />
                      <span>{subject.name}</span>
                    </div>
                    <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6 p-0"
                        onClick={(e) => {
                          e.stopPropagation();
                          setEditingSubject(subject);
                          setIsEditDialogOpen(true);
                        }}
                      >
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6 p-0 text-destructive"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteSubject(subject);
                        }}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="py-6 text-center text-muted-foreground">
                No subjects found. Add one below.
              </div>
            )}
            <div className="p-1 border-t">
              <Button
                variant="ghost"
                className="w-full justify-start gap-2"
                onClick={() => {
                  setIsDropdownOpen(false);
                  setIsAddDialogOpen(true);
                }}
              >
                <Plus className="h-4 w-4" />
                <span>Add new subject</span>
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>

      {/* Add Subject Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={(open) => {
        setIsAddDialogOpen(open);
        if (!open) {
          // Reset color when dialog closes
          setNewSubjectColor(PREDEFINED_COLORS[Math.floor(Math.random() * PREDEFINED_COLORS.length)]);
        }
      }}>
        <DialogContent className="sm:max-w-[425px] bg-background dark:bg-[#1a1f3c]/90 backdrop-blur-md border border-border dark:border-white/10 text-foreground dark:text-white">
          <DialogHeader>
            <DialogTitle>Add New Subject</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <label htmlFor="subject-name" className="text-sm font-medium">
                Subject Name
              </label>
              <Input
                id="subject-name"
                value={newSubjectName}
                onChange={(e) => setNewSubjectName(e.target.value)}
                placeholder="Enter subject name"
                className="col-span-3"
              />
            </div>

            {/* Color Preview */}
            <div className="mb-2">
              <label className="text-sm font-medium mb-2 block">
                Color Preview
              </label>
              <div className="flex items-center gap-4">
                <div
                  className="w-16 h-16 rounded-md shadow-md border border-border"
                  style={{ backgroundColor: newSubjectColor }}
                />
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: newSubjectColor }}
                    />
                    <span className="font-medium">{newSubjectName || "Subject Name"}</span>
                  </div>
                  <div
                    className="text-xs px-2 py-1 rounded-md inline-block"
                    style={{
                      backgroundColor: `${newSubjectColor}20`,
                      color: newSubjectColor,
                      border: `1px solid ${newSubjectColor}40`
                    }}
                  >
                    Preview Tag
                  </div>
                </div>
              </div>
            </div>

            {/* Color Selection */}
            <div className="grid gap-2">
              <label htmlFor="new-subject-color" className="text-sm font-medium">
                Subject Color
              </label>

              {/* Predefined Color Palette */}
              <div className="flex flex-wrap gap-2 mb-3">
                {PREDEFINED_COLORS.map((color) => (
                  <button
                    key={color}
                    type="button"
                    className={`w-8 h-8 rounded-md cursor-pointer transition-all ${newSubjectColor === color ? 'ring-2 ring-primary ring-offset-2' : 'hover:scale-110'}`}
                    style={{ backgroundColor: color }}
                    onClick={() => setNewSubjectColor(color)}
                    aria-label={`Select color ${color}`}
                  />
                ))}
              </div>

              {/* Custom Color Picker */}
              <div className="border rounded-md p-3 bg-muted/30">
                <p className="text-xs text-muted-foreground mb-2">Custom Color</p>
                <div className="flex gap-3 items-center">
                  <HexColorPicker
                    color={newSubjectColor}
                    onChange={setNewSubjectColor}
                    style={{ width: '100%', height: '120px' }}
                  />
                  <div className="flex flex-col gap-2">
                    <div
                      className="w-10 h-10 rounded-md shadow-sm border border-border"
                      style={{ backgroundColor: newSubjectColor }}
                    />
                    <Input
                      value={newSubjectColor}
                      onChange={(e) => setNewSubjectColor(e.target.value)}
                      className="w-24 text-xs"
                    />
                  </div>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={() => handleAddSubject()} disabled={!newSubjectName.trim()}>
                Add Subject
              </Button>
            </DialogFooter>
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Subject Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px] bg-background dark:bg-[#1a1f3c]/90 backdrop-blur-md border border-border dark:border-white/10 text-foreground dark:text-white">
          <DialogHeader>
            <DialogTitle>Edit Subject</DialogTitle>
          </DialogHeader>
          {editingSubject && (
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <label htmlFor="edit-subject-name" className="text-sm font-medium">
                  Subject Name
                </label>
                <Input
                  id="edit-subject-name"
                  value={editingSubject.name}
                  onChange={(e) => setEditingSubject({ ...editingSubject, name: e.target.value })}
                  placeholder="Enter subject name"
                  className="col-span-3"
                />
              </div>

              {/* Color Preview */}
              <div className="mb-2">
                <label className="text-sm font-medium mb-2 block">
                  Color Preview
                </label>
                <div className="flex items-center gap-4">
                  <div
                    className="w-16 h-16 rounded-md shadow-md border border-border"
                    style={{ backgroundColor: editingSubject.color }}
                  />
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: editingSubject.color }}
                      />
                      <span className="font-medium">{editingSubject.name}</span>
                    </div>
                    <div
                      className="text-xs px-2 py-1 rounded-md inline-block"
                      style={{
                        backgroundColor: `${editingSubject.color}20`,
                        color: editingSubject.color,
                        border: `1px solid ${editingSubject.color}40`
                      }}
                    >
                      Preview Tag
                    </div>
                  </div>
                </div>
              </div>

              {/* Color Selection */}
              <div className="grid gap-2">
                <label htmlFor="edit-subject-color" className="text-sm font-medium">
                  Subject Color
                </label>

                {/* Predefined Color Palette */}
                <div className="flex flex-wrap gap-2 mb-3">
                  {PREDEFINED_COLORS.map((color) => (
                    <button
                      key={color}
                      type="button"
                      className={`w-8 h-8 rounded-md cursor-pointer transition-all ${editingSubject.color === color ? 'ring-2 ring-primary ring-offset-2' : 'hover:scale-110'}`}
                      style={{ backgroundColor: color }}
                      onClick={() => setEditingSubject({ ...editingSubject, color })}
                      aria-label={`Select color ${color}`}
                    />
                  ))}
                </div>

                {/* Custom Color Picker */}
                <div className="border rounded-md p-3 bg-muted/30">
                  <p className="text-xs text-muted-foreground mb-2">Custom Color</p>
                  <div className="flex gap-3 items-center">
                    <HexColorPicker
                      color={editingSubject.color}
                      onChange={(color) => setEditingSubject({ ...editingSubject, color })}
                      style={{ width: '100%', height: '120px' }}
                    />
                    <div className="flex flex-col gap-2">
                      <div
                        className="w-10 h-10 rounded-md shadow-sm border border-border"
                        style={{ backgroundColor: editingSubject.color }}
                      />
                      <Input
                        value={editingSubject.color}
                        onChange={(e) => setEditingSubject({ ...editingSubject, color: e.target.value })}
                        className="w-24 text-xs"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={() => handleEditSubject()} disabled={!editingSubject || !editingSubject.name.trim()}>
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
